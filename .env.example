# Web Design Image MCP Server Configuration
# Copy this file to .env and fill in your values

# ModelScope API Configuration
# Note: In production, the API key should be provided by the MCP client
# This is only for development/testing purposes
MODELSCOPE_API_KEY=your_modelscope_api_key_here

# Hyperbolic API Configuration
# Alternative image generation provider
HYPERBOLIC_API_KEY=your_hyperbolic_api_key_here

# Server Configuration
SERVER_NAME=Web Design Image Generator
SERVER_VERSION=0.1.0
LOG_LEVEL=INFO

# Image Generation Settings
DEFAULT_IMAGE_WIDTH=1024
DEFAULT_IMAGE_HEIGHT=1024
MAX_IMAGE_SIZE=2048
SUPPORTED_FORMATS=webp,png,jpeg,avif

# Cache Settings
CACHE_ENABLED=true
CACHE_DIR=./cache/images
CACHE_MAX_SIZE_MB=1000
CACHE_TTL_HOURS=24

# Rate Limiting
MAX_REQUESTS_PER_MINUTE=10
MAX_CONCURRENT_REQUESTS=3

# Retry Configuration
MAX_RETRIES=3
RETRY_DELAY_SECONDS=1
BACKOFF_MULTIPLIER=2

# Development Settings
DEBUG=false
DEVELOPMENT_MODE=false
