#!/usr/bin/env python3
"""
Test Hyperbolic API integration.
"""

import os
import asyncio
import sys
sys.path.insert(0, 'src')

from web_design_image_mcp.services.hyperbolic_client import HyperbolicClient
from web_design_image_mcp.services.unified_client import UnifiedImageClient, ImageProvider, UnifiedImageRequest

async def test_hyperbolic_direct():
    """Test Hyperbolic client directly."""
    print("🧪 Testing Hyperbolic Client Direct")
    print("=" * 50)
    
    api_key = os.getenv('HYPERBOLIC_API_KEY')
    if not api_key:
        print("❌ No HYPERBOLIC_API_KEY found in environment")
        return False
    
    print(f"🔑 Using API key: {api_key[:10]}...")
    
    client = HyperbolicClient(api_key=api_key)
    
    try:
        print("🚀 Generating test image...")
        result = await client.generate_image(
            prompt="A beautiful sunset over a calm ocean",
            resolution="512x512",
            steps=20,
            cfg_scale=5.0
        )
        
        print("✅ Hyperbolic generation successful!")
        print(f"🖼️  Image URL: {result.image_url}")
        print(f"📏 Image size: {len(result.image_data)} bytes")
        print(f"⏱️  Generation time: {result.generation_time}")
        
        # Save image
        with open('hyperbolic_test.jpg', 'wb') as f:
            f.write(result.image_data)
        print("💾 Image saved as hyperbolic_test.jpg")
        
        return True
        
    except Exception as e:
        print(f"❌ Hyperbolic test failed: {e}")
        return False
    finally:
        await client.close()

async def test_unified_client():
    """Test unified client with both providers."""
    print("\n🧪 Testing Unified Client")
    print("=" * 50)
    
    modelscope_key = os.getenv('MODELSCOPE_API_KEY')
    hyperbolic_key = os.getenv('HYPERBOLIC_API_KEY')
    
    if not hyperbolic_key:
        print("❌ No HYPERBOLIC_API_KEY found")
        return False
    
    client = UnifiedImageClient()
    
    try:
        # Test provider info
        print("📋 Available providers:")
        providers = client.get_available_providers()
        for provider in providers:
            info = client.get_provider_info(ImageProvider(provider))
            print(f"  • {info['name']}: {info['description']}")
        
        # Test Hyperbolic
        print("\n🚀 Testing Hyperbolic via unified client...")
        request = UnifiedImageRequest(
            prompt="A golden cat sitting in a garden",
            provider=ImageProvider.HYPERBOLIC,
            width=512,
            height=512,
            steps=15,
            guidance_scale=4.0,
            negative_prompt="blurry, low quality",
            api_key=hyperbolic_key
        )
        
        result = await client.generate_image(request)
        
        print("✅ Unified Hyperbolic generation successful!")
        print(f"🖼️  Image URL: {result.image_url}")
        print(f"📏 Image size: {len(result.image_data)} bytes")
        print(f"🏷️  Provider: {result.provider.value}")
        
        # Save image
        with open('unified_hyperbolic_test.jpg', 'wb') as f:
            f.write(result.image_data)
        print("💾 Image saved as unified_hyperbolic_test.jpg")
        
        # Test ModelScope if key available
        if modelscope_key:
            print("\n🚀 Testing ModelScope via unified client...")
            request.provider = ImageProvider.MODELSCOPE
            request.api_key = modelscope_key
            
            try:
                result = await client.generate_image(request)
                print("✅ Unified ModelScope generation successful!")
                print(f"🏷️  Provider: {result.provider.value}")
            except Exception as e:
                print(f"⚠️  ModelScope test failed (expected): {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Unified client test failed: {e}")
        return False
    finally:
        await client.close()

async def test_server_integration():
    """Test server integration with new tools."""
    print("\n🧪 Testing Server Integration")
    print("=" * 50)
    
    try:
        from web_design_image_mcp.server import create_server
        
        server = create_server()
        print("✅ Server created successfully with Hyperbolic support")
        
        # Check if new tools are available
        print("🔧 Server tools available")
        
        return True
        
    except Exception as e:
        print(f"❌ Server integration test failed: {e}")
        return False

async def main():
    """Run all tests."""
    print("🎯 Hyperbolic Integration Test Suite")
    print("=" * 60)
    
    results = []
    
    # Test 1: Direct Hyperbolic client
    results.append(await test_hyperbolic_direct())
    
    # Test 2: Unified client
    results.append(await test_unified_client())
    
    # Test 3: Server integration
    results.append(await test_server_integration())
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    test_names = ["Hyperbolic Direct", "Unified Client", "Server Integration"]
    
    for i, (name, result) in enumerate(zip(test_names, results)):
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {i+1}. {name}: {status}")
    
    passed = sum(results)
    total = len(results)
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Hyperbolic integration is working!")
    elif passed > 0:
        print("⚠️  Some tests passed. Check API keys and configuration.")
    else:
        print("💥 All tests failed. Check setup and API keys.")

if __name__ == "__main__":
    asyncio.run(main())
