#!/usr/bin/env python3
"""
Test the enhanced API with new parameters (steps, guidance_scale).
"""

import os
import requests
import json
from PIL import Image
from io import Bytes<PERSON>

def test_enhanced_modelscope_api():
    """Test ModelScope API with enhanced parameters."""
    
    # Get API key from environment
    api_key = os.getenv('MODELSCOPE_API_KEY')
    if not api_key:
        print("❌ No API key found in environment variable MODELSCOPE_API_KEY")
        return False
    
    print(f"🔑 Using API key: {api_key[:10]}...")
    
    url = 'https://api-inference.modelscope.cn/v1/images/generations'
    
    # Test with enhanced parameters
    payload = {
        'model': 'MusePublic/489_ckpt_FLUX_1',
        'prompt': 'A professional business team meeting in a modern office, high quality',
        'width': 1024,
        'height': 1024,
        'steps': 20,
        'guidance_scale': 7.5
    }
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    print("🚀 Making enhanced API request...")
    print(f"URL: {url}")
    print(f"Model: {payload['model']}")
    print(f"Prompt: {payload['prompt']}")
    print(f"Size: {payload['width']}x{payload['height']}")
    print(f"Steps: {payload['steps']}")
    print(f"Guidance Scale: {payload['guidance_scale']}")
    
    try:
        response = requests.post(
            url, 
            data=json.dumps(payload, ensure_ascii=False).encode('utf-8'), 
            headers=headers,
            timeout=120  # Longer timeout for enhanced generation
        )
        
        print(f"📊 Response status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ Enhanced API request successful!")
            print(f"📄 Response data keys: {list(response_data.keys())}")
            
            # Try to download and save the image
            if 'images' in response_data and response_data['images']:
                image_url = response_data['images'][0]['url']
                print(f"🖼️  Downloading enhanced image from: {image_url}")
                
                image_response = requests.get(image_url)
                if image_response.status_code == 200:
                    image = Image.open(BytesIO(image_response.content))
                    output_path = 'enhanced_result_image.jpg'
                    image.save(output_path)
                    print(f"✅ Enhanced image saved to: {output_path}")
                    print(f"📏 Image size: {image.size}")
                    return True
                else:
                    print(f"❌ Failed to download image: {image_response.status_code}")
                    return False
            else:
                print("❌ No images in response")
                return False
        else:
            print(f"❌ Enhanced API request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during enhanced API request: {e}")
        return False

def test_basic_api():
    """Test basic API without enhanced parameters."""
    
    api_key = os.getenv('MODELSCOPE_API_KEY')
    if not api_key:
        return False
    
    url = 'https://api-inference.modelscope.cn/v1/images/generations'
    
    # Basic payload without enhanced parameters
    payload = {
        'model': 'MusePublic/489_ckpt_FLUX_1',
        'prompt': 'A simple golden cat'
    }
    
    headers = {
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }
    
    print("🚀 Making basic API request...")
    
    try:
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        print(f"📊 Basic response status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ Basic API request successful!")
            return True
        else:
            print(f"❌ Basic API request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during basic API request: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Enhanced ModelScope API")
    print("=" * 60)
    
    print("\n1️⃣ Testing basic API...")
    basic_success = test_basic_api()
    
    print("\n2️⃣ Testing enhanced API with new parameters...")
    enhanced_success = test_enhanced_modelscope_api()
    
    print("=" * 60)
    print(f"📊 Results:")
    print(f"   Basic API: {'✅ Success' if basic_success else '❌ Failed'}")
    print(f"   Enhanced API: {'✅ Success' if enhanced_success else '❌ Failed'}")
    
    if enhanced_success:
        print("🎉 Enhanced API test completed successfully!")
    elif basic_success:
        print("⚠️ Basic API works, but enhanced parameters may not be supported")
    else:
        print("💥 Both tests failed - API key may need proper setup")
