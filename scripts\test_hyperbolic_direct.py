#!/usr/bin/env python3
"""
Direct test of Hyperbolic API with manual API key input.
"""

import requests
import json
from PIL import Image
from io import BytesIO

def test_hyperbolic_api():
    """Test Hyperbolic API directly."""
    
    # You can paste your API key here for testing
    api_key = input("Please enter your Hyperbolic API key: ").strip()
    
    if not api_key:
        print("❌ No API key provided")
        return False
    
    print(f"🔑 Using API key: {api_key[:10]}...")
    
    url = "https://api.hyperbolic.xyz/v1/image/generation"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    data = {
        "model_name": "FLUX.1-dev",
        "prompt": "A beautiful sunset over a calm ocean",
        "enable_refiner": "false",
        "negative_prompt": "",
        "strength": "0.8",
        "steps": "30",
        "cfg_scale": "5",
        "resolution": "1024x1024",
        "backend": "auto"
    }
    
    print("🚀 Making Hyperbolic API request...")
    print(f"URL: {url}")
    print(f"Model: {data['model_name']}")
    print(f"Prompt: {data['prompt']}")
    print(f"Resolution: {data['resolution']}")
    print(f"Steps: {data['steps']}")
    print(f"CFG Scale: {data['cfg_scale']}")
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=120)
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            response_data = response.json()
            print("✅ Hyperbolic API request successful!")
            print(f"📄 Response data: {json.dumps(response_data, indent=2)}")
            
            # Try to find and download the image
            image_url = None
            if 'image_url' in response_data:
                image_url = response_data['image_url']
            elif 'images' in response_data and response_data['images']:
                image_url = response_data['images'][0].get('url')
            elif 'data' in response_data and response_data['data']:
                image_url = response_data['data'][0].get('url')
            
            if image_url:
                print(f"🖼️  Downloading image from: {image_url}")
                
                image_response = requests.get(image_url)
                if image_response.status_code == 200:
                    image = Image.open(BytesIO(image_response.content))
                    output_path = 'hyperbolic_result_image.jpg'
                    image.save(output_path)
                    print(f"✅ Image saved to: {output_path}")
                    print(f"📏 Image size: {image.size}")
                    return True
                else:
                    print(f"❌ Failed to download image: {image_response.status_code}")
                    return False
            else:
                print("❌ No image URL found in response")
                print("🔍 Available keys:", list(response_data.keys()))
                return False
        else:
            print(f"❌ Hyperbolic API request failed: {response.status_code}")
            print(f"📄 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error during Hyperbolic API request: {e}")
        return False

def test_with_different_params():
    """Test with different parameters."""
    
    api_key = input("Please enter your Hyperbolic API key for parameter test: ").strip()
    
    if not api_key:
        print("❌ No API key provided")
        return False
    
    url = "https://api.hyperbolic.xyz/v1/image/generation"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {api_key}"
    }
    
    # Test with different parameters
    test_cases = [
        {
            "name": "Simple Cat",
            "data": {
                "model_name": "FLUX.1-dev",
                "prompt": "A golden cat sitting in a garden",
                "resolution": "512x512",
                "steps": "20",
                "cfg_scale": "4"
            }
        },
        {
            "name": "Professional Business",
            "data": {
                "model_name": "FLUX.1-dev",
                "prompt": "A professional business team meeting in a modern office",
                "negative_prompt": "blurry, low quality, cartoon",
                "resolution": "1024x1024",
                "steps": "25",
                "cfg_scale": "6",
                "strength": "0.9"
            }
        }
    ]
    
    for i, test_case in enumerate(test_cases):
        print(f"\n🧪 Test {i+1}: {test_case['name']}")
        print("-" * 40)
        
        try:
            response = requests.post(url, headers=headers, json=test_case['data'], timeout=120)
            
            if response.status_code == 200:
                print(f"✅ {test_case['name']} - Success!")
                response_data = response.json()
                
                # Try to save image
                image_url = response_data.get('image_url') or response_data.get('images', [{}])[0].get('url')
                if image_url:
                    image_response = requests.get(image_url)
                    if image_response.status_code == 200:
                        image = Image.open(BytesIO(image_response.content))
                        output_path = f'hyperbolic_test_{i+1}.jpg'
                        image.save(output_path)
                        print(f"💾 Saved as: {output_path}")
            else:
                print(f"❌ {test_case['name']} - Failed: {response.status_code}")
                print(f"📄 Response: {response.text}")
                
        except Exception as e:
            print(f"❌ {test_case['name']} - Error: {e}")
    
    return True

if __name__ == "__main__":
    print("🧪 Hyperbolic API Direct Test")
    print("=" * 50)
    
    print("\n1️⃣ Basic API Test")
    basic_success = test_hyperbolic_api()
    
    if basic_success:
        print("\n2️⃣ Parameter Variation Test")
        test_with_different_params()
    
    print("\n" + "=" * 50)
    if basic_success:
        print("🎉 Hyperbolic API is working!")
    else:
        print("💥 Hyperbolic API test failed!")
