<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">81%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button current">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-13 20:31 -0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79___init___py.html">src\web_design_image_mcp\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html">src\web_design_image_mcp\config.py</a></td>
                <td>41</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="40 41">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html">src\web_design_image_mcp\server.py</a></td>
                <td>118</td>
                <td>36</td>
                <td>2</td>
                <td class="right" data-ratio="82 118">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627___init___py.html">src\web_design_image_mcp\services\__init__.py</a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html">src\web_design_image_mcp\services\api_client.py</a></td>
                <td>87</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="86 87">99%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td>225</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="184 225">82%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td>147</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="85 147">58%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td>118</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="115 118">97%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td>746</td>
                <td>144</td>
                <td>2</td>
                <td class="right" data-ratio="602 746">81%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-13 20:31 -0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href="z_85a35d5be7025627_prompt_generator_py.html"></a>
        <a id="nextFileLink" class="nav" href="z_b34453d2780a7b79___init___py.html"></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
