{"tests/test_api_client.py::TestModelScopeClient": true, "tests/test_api_client.py::TestImageGenerationRequest": true, "tests/test_api_client.py::TestImageGenerationResult": true, "tests/test_context_analyzer.py::TestWebContextAnalyzer": true, "tests/test_integration.py::TestCompleteWorkflow": true, "tests/test_integration.py::TestResourceAccess": true, "tests/test_performance.py::TestPerformance": true, "tests/test_performance.py::TestScalability": true, "tests/test_prompt_generator.py::TestPromptGenerator": true, "tests/test_prompt_generator.py::TestImageContext": true, "tests/test_prompt_generator.py::TestEnums": true, "tests/test_server.py::test_server_tools_exist": true, "tests/test_api_client.py::TestModelScopeClient::test_initialization_without_api_key": true, "tests/test_api_client.py::TestModelScopeClient::test_generate_image_no_api_key": true, "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_analyze_basic_html": true, "tests/test_integration.py::TestCompleteWorkflow::test_analyze_web_context_workflow": true, "tests/test_integration.py::TestCompleteWorkflow::test_generate_web_image_workflow": true, "tests/test_integration.py::TestCompleteWorkflow::test_end_to_end_workflow": true, "tests/test_integration.py::TestCompleteWorkflow::test_server_creation_and_tools": true, "tests/test_performance.py::TestPerformance::test_image_processing_performance": true}