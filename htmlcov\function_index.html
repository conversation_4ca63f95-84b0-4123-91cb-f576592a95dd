<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">81%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button current">Functions</a>
                <a class="button" href="class_index.html">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-13 20:31 -0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">function<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79___init___py.html">src\web_design_image_mcp\__init__.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html#t42">src\web_design_image_mcp\config.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html#t42"><data value='parse_supported_formats'>ServerConfig.parse_supported_formats</data></a></td>
                <td>3</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="2 3">67%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html#t74">src\web_design_image_mcp\config.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html#t74"><data value='init__'>ServerConfig.__init__</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html">src\web_design_image_mcp\config.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t42">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t42"><data value='server_lifespan'>server_lifespan</data></a></td>
                <td>13</td>
                <td>13</td>
                <td>0</td>
                <td class="right" data-ratio="0 13">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t67">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t67"><data value='create_server'>create_server</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t88">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t88"><data value='main'>main</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t101">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t101"><data value='add_image_generation_tools'>_add_image_generation_tools</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t105">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t105"><data value='generate_web_image'>_add_image_generation_tools.generate_web_image</data></a></td>
                <td>15</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="11 15">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t186">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t186"><data value='add_context_analysis_tools'>_add_context_analysis_tools</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t190">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t190"><data value='analyze_web_context'>_add_context_analysis_tools.analyze_web_context</data></a></td>
                <td>6</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="2 6">33%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t238">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t238"><data value='add_prompt_tools'>_add_prompt_tools</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t242">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t242"><data value='suggest_image_prompts'>_add_prompt_tools.suggest_image_prompts</data></a></td>
                <td>15</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="13 15">87%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t315">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t315"><data value='add_resources'>_add_resources</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t319">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t319"><data value='get_image_types'>_add_resources.get_image_types</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t358">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t358"><data value='get_website_categories'>_add_resources.get_website_categories</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t392">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t392"><data value='get_prompt_examples'>_add_resources.get_prompt_examples</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t432">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t432"><data value='get_server_info'>_add_resources.get_server_info</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>27</td>
                <td>0</td>
                <td>2</td>
                <td class="right" data-ratio="27 27">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627___init___py.html">src\web_design_image_mcp\services\__init__.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627___init___py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t49">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t49"><data value='init__'>ModelScopeClient.__init__</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t71">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t71"><data value='generate_image'>ModelScopeClient.generate_image</data></a></td>
                <td>17</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="16 17">94%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t123">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t123"><data value='make_api_request'>ModelScopeClient._make_api_request</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t177">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t177"><data value='download_image'>ModelScopeClient._download_image</data></a></td>
                <td>6</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="6 6">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t186">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t186"><data value='validate_image'>ModelScopeClient.validate_image</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t196">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t196"><data value='close'>ModelScopeClient.close</data></a></td>
                <td>2</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="2 2">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t44">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t44"><data value='init__'>WebContextAnalyzer.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t51">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t51"><data value='analyze_html'>WebContextAnalyzer.analyze_html</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t95">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t95"><data value='suggest_image_context'>WebContextAnalyzer.suggest_image_context</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t139">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t139"><data value='extract_title'>WebContextAnalyzer._extract_title</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t152">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t152"><data value='extract_description'>WebContextAnalyzer._extract_description</data></a></td>
                <td>11</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="10 11">91%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t172">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t172"><data value='classify_website_category'>WebContextAnalyzer._classify_website_category</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t203">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t203"><data value='extract_color_scheme'>WebContextAnalyzer._extract_color_scheme</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t222">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t222"><data value='extract_style_attributes'>WebContextAnalyzer._extract_style_attributes</data></a></td>
                <td>14</td>
                <td>2</td>
                <td>0</td>
                <td class="right" data-ratio="12 14">86%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t244">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t244"><data value='find_image_placeholders'>WebContextAnalyzer._find_image_placeholders</data></a></td>
                <td>10</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="10 10">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t262">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t262"><data value='analyze_img_element'>WebContextAnalyzer._analyze_img_element</data></a></td>
                <td>11</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="11 11">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t303">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t303"><data value='analyze_div_element'>WebContextAnalyzer._analyze_div_element</data></a></td>
                <td>9</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="9 9">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t339">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t339"><data value='determine_image_type'>WebContextAnalyzer._determine_image_type</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t356">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t356"><data value='get_context_text'>WebContextAnalyzer._get_context_text</data></a></td>
                <td>16</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="12 16">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t382">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t382"><data value='extract_dimensions'>WebContextAnalyzer._extract_dimensions</data></a></td>
                <td>16</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="12 16">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t406">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t406"><data value='extract_semantic_sections'>WebContextAnalyzer._extract_semantic_sections</data></a></td>
                <td>7</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="7 7">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t420">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t420"><data value='generate_content_description'>WebContextAnalyzer._generate_content_description</data></a></td>
                <td>8</td>
                <td>8</td>
                <td>0</td>
                <td class="right" data-ratio="0 8">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t442">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t442"><data value='select_style_keywords'>WebContextAnalyzer._select_style_keywords</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t464">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t464"><data value='extract_brand_attributes'>WebContextAnalyzer._extract_brand_attributes</data></a></td>
                <td>4</td>
                <td>4</td>
                <td>0</td>
                <td class="right" data-ratio="0 4">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t480">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t480"><data value='load_image_type_patterns'>WebContextAnalyzer._load_image_type_patterns</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t492">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t492"><data value='load_category_keywords'>WebContextAnalyzer._load_category_keywords</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t503">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t503"><data value='load_color_extractors'>WebContextAnalyzer._load_color_extractors</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>47</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="47 47">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t46">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t46"><data value='init__'>ImageProcessor.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t61">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t61"><data value='process_image'>ImageProcessor.process_image</data></a></td>
                <td>22</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="12 22">55%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t130">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t130"><data value='create_responsive_set'>ImageProcessor.create_responsive_set</data></a></td>
                <td>10</td>
                <td>10</td>
                <td>0</td>
                <td class="right" data-ratio="0 10">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t164">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t164"><data value='resize_image'>ImageProcessor._resize_image</data></a></td>
                <td>11</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="8 11">73%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t189">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t189"><data value='optimize_image'>ImageProcessor._optimize_image</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t197">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t197"><data value='generate_cache_key'>ImageProcessor._generate_cache_key</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t216">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t216"><data value='get_cached_image'>ImageProcessor._get_cached_image</data></a></td>
                <td>12</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="3 12">25%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t236">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t236"><data value='save_image'>ImageProcessor._save_image</data></a></td>
                <td>8</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="8 8">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t258">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t258"><data value='create_processed_result'>ImageProcessor._create_processed_result</data></a></td>
                <td>7</td>
                <td>6</td>
                <td>0</td>
                <td class="right" data-ratio="1 7">14%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t282">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t282"><data value='cleanup_cache'>ImageProcessor.cleanup_cache</data></a></td>
                <td>15</td>
                <td>15</td>
                <td>0</td>
                <td class="right" data-ratio="0 15">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t303">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t303"><data value='get_cache_stats'>ImageProcessor.get_cache_stats</data></a></td>
                <td>9</td>
                <td>9</td>
                <td>0</td>
                <td class="right" data-ratio="0 9">0%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t66">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t66"><data value='init__'>PromptGenerator.__init__</data></a></td>
                <td>4</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="4 4">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t73">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t73"><data value='generate_prompt'>PromptGenerator.generate_prompt</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t94">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t94"><data value='generate_from_template'>PromptGenerator._generate_from_template</data></a></td>
                <td>20</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="20 20">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t141">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t141"><data value='enhance_prompt'>PromptGenerator._enhance_prompt</data></a></td>
                <td>12</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="9 12">75%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t166">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t166"><data value='select_style_modifiers'>PromptGenerator._select_style_modifiers</data></a></td>
                <td>14</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="14 14">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t192">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t192"><data value='optimize_for_flux'>PromptGenerator._optimize_for_flux</data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t210">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t210"><data value='load_templates'>PromptGenerator._load_templates</data></a></td>
                <td>3</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="3 3">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t249">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t249"><data value='load_style_keywords'>PromptGenerator._load_style_keywords</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t263">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t263"><data value='load_quality_modifiers'>PromptGenerator._load_quality_modifiers</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t272">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t272"><data value='get_default_template'>PromptGenerator._get_default_template</data></a></td>
                <td>1</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="1 1">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html"><data value=''><span class='no-noun'>(no function)</span></data></a></td>
                <td>54</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="54 54">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>746</td>
                <td>144</td>
                <td>2</td>
                <td class="right" data-ratio="602 746">81%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-13 20:31 -0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
