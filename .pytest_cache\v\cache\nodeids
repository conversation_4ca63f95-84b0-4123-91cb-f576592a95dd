["tests/test_api_client.py::TestImageGenerationRequest::test_request_creation_custom", "tests/test_api_client.py::TestImageGenerationRequest::test_request_creation_defaults", "tests/test_api_client.py::TestImageGenerationResult::test_result_creation", "tests/test_api_client.py::TestModelScopeClient::test_close_client", "tests/test_api_client.py::TestModelScopeClient::test_generate_image_api_error", "tests/test_api_client.py::TestModelScopeClient::test_generate_image_download_error", "tests/test_api_client.py::TestModelScopeClient::test_generate_image_invalid_response", "tests/test_api_client.py::TestModelScopeClient::test_generate_image_max_retries_exceeded", "tests/test_api_client.py::TestModelScopeClient::test_generate_image_no_api_key", "tests/test_api_client.py::TestModelScopeClient::test_generate_image_retry_logic", "tests/test_api_client.py::TestModelScopeClient::test_generate_image_success", "tests/test_api_client.py::TestModelScopeClient::test_generate_image_with_custom_dimensions", "tests/test_api_client.py::TestModelScopeClient::test_generate_image_with_override_api_key", "tests/test_api_client.py::TestModelScopeClient::test_initialization", "tests/test_api_client.py::TestModelScopeClient::test_initialization_without_api_key", "tests/test_api_client.py::TestModelScopeClient::test_validate_image_invalid", "tests/test_api_client.py::TestModelScopeClient::test_validate_image_valid", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_analyze_basic_html", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_color_scheme_extraction", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_context_text_extraction", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_description_extraction", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_dimensions_extraction", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_div_element_analysis", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_empty_html", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_image_placeholder_detection", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_image_type_determination", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_img_element_analysis", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_initialization", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_malformed_html", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_semantic_sections_extraction", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_style_attributes_detection", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_suggest_image_context", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_title_extraction", "tests/test_context_analyzer.py::TestWebContextAnalyzer::test_website_category_classification", "tests/test_integration.py::TestCompleteWorkflow::test_analyze_web_context_workflow", "tests/test_integration.py::TestCompleteWorkflow::test_end_to_end_workflow", "tests/test_integration.py::TestCompleteWorkflow::test_error_handling_workflow", "tests/test_integration.py::TestCompleteWorkflow::test_generate_web_image_workflow", "tests/test_integration.py::TestCompleteWorkflow::test_server_creation_and_tools", "tests/test_integration.py::TestCompleteWorkflow::test_suggest_image_prompts_workflow", "tests/test_integration.py::TestResourceAccess::test_resource_templates_access", "tests/test_performance.py::TestPerformance::test_api_client_retry_performance", "tests/test_performance.py::TestPerformance::test_cache_performance", "tests/test_performance.py::TestPerformance::test_concurrent_prompt_generation", "tests/test_performance.py::TestPerformance::test_enum_performance", "tests/test_performance.py::TestPerformance::test_image_processing_performance", "tests/test_performance.py::TestPerformance::test_large_html_analysis_performance", "tests/test_performance.py::TestPerformance::test_memory_usage_prompt_generation", "tests/test_performance.py::TestPerformance::test_prompt_generation_performance", "tests/test_performance.py::TestPerformance::test_web_analysis_performance", "tests/test_performance.py::TestScalability::test_concurrent_web_analysis", "tests/test_performance.py::TestScalability::test_memory_usage_under_load", "tests/test_prompt_generator.py::TestEnums::test_enum_invalid_values", "tests/test_prompt_generator.py::TestEnums::test_image_type_enum_values", "tests/test_prompt_generator.py::TestEnums::test_website_category_enum_values", "tests/test_prompt_generator.py::TestImageContext::test_image_context_creation", "tests/test_prompt_generator.py::TestImageContext::test_image_context_minimal", "tests/test_prompt_generator.py::TestPromptGenerator::test_brand_attributes", "tests/test_prompt_generator.py::TestPromptGenerator::test_color_scheme_inclusion", "tests/test_prompt_generator.py::TestPromptGenerator::test_different_image_types", "tests/test_prompt_generator.py::TestPromptGenerator::test_different_website_categories", "tests/test_prompt_generator.py::TestPromptGenerator::test_empty_content_description", "tests/test_prompt_generator.py::TestPromptGenerator::test_flux_optimization", "tests/test_prompt_generator.py::TestPromptGenerator::test_generate_prompt_with_context", "tests/test_prompt_generator.py::TestPromptGenerator::test_generate_prompt_with_custom_prompt", "tests/test_prompt_generator.py::TestPromptGenerator::test_initialization", "tests/test_prompt_generator.py::TestPromptGenerator::test_style_keywords_inclusion", "tests/test_prompt_generator.py::TestPromptGenerator::test_technical_specs_inclusion", "tests/test_server.py::test_create_server", "tests/test_server.py::test_image_type_enum", "tests/test_server.py::test_server_tools_exist", "tests/test_server.py::test_website_category_enum"]