"""
Unified image generation client supporting multiple providers.
"""

import logging
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Optional, Union

from .api_client import ModelScopeClient, ImageGenerationResult
from .hyperbolic_client import HyperbolicClient, HyperbolicImageResult

logger = logging.getLogger(__name__)


class ImageProvider(Enum):
    """Supported image generation providers."""
    MODELSCOPE = "modelscope"
    HYPERBOLIC = "hyperbolic"


@dataclass
class UnifiedImageRequest:
    """Unified request parameters for image generation."""
    prompt: str
    provider: ImageProvider = ImageProvider.MODELSCOPE
    width: int = 1024
    height: int = 1024
    steps: Optional[int] = None
    guidance_scale: Optional[float] = None
    negative_prompt: str = ""
    api_key: Optional[str] = None


@dataclass
class UnifiedImageResult:
    """Unified result from image generation."""
    image_url: str
    image_data: bytes
    metadata: dict
    generation_time: datetime
    provider: ImageProvider
    original_result: Union[ImageGenerationResult, HyperbolicImageResult]


class UnifiedImageClient:
    """Unified client for multiple image generation providers."""
    
    def __init__(self):
        """Initialize the unified client."""
        self.modelscope_client = ModelScopeClient()
        self.hyperbolic_client = HyperbolicClient()
        
        logger.info("Unified image client initialized")
    
    async def generate_image(self, request: UnifiedImageRequest) -> UnifiedImageResult:
        """Generate an image using the specified provider.
        
        Args:
            request: Unified image generation request
        
        Returns:
            UnifiedImageResult with the generated image and metadata
        
        Raises:
            ValueError: If provider is not supported
            Exception: If image generation fails
        """
        if request.provider == ImageProvider.MODELSCOPE:
            return await self._generate_with_modelscope(request)
        elif request.provider == ImageProvider.HYPERBOLIC:
            return await self._generate_with_hyperbolic(request)
        else:
            raise ValueError(f"Unsupported provider: {request.provider}")
    
    async def _generate_with_modelscope(self, request: UnifiedImageRequest) -> UnifiedImageResult:
        """Generate image using ModelScope."""
        result = await self.modelscope_client.generate_image(
            prompt=request.prompt,
            width=request.width,
            height=request.height,
            steps=request.steps,
            guidance_scale=request.guidance_scale,
            api_key=request.api_key
        )
        
        return UnifiedImageResult(
            image_url=result.image_url,
            image_data=result.image_data,
            metadata={
                **result.metadata,
                'provider': 'modelscope',
                'negative_prompt': request.negative_prompt
            },
            generation_time=result.generation_time,
            provider=ImageProvider.MODELSCOPE,
            original_result=result
        )
    
    async def _generate_with_hyperbolic(self, request: UnifiedImageRequest) -> UnifiedImageResult:
        """Generate image using Hyperbolic."""

        result = await self.hyperbolic_client.generate_image(
            prompt=request.prompt,
            width=request.width,
            height=request.height,
            steps=request.steps or 30,
            cfg_scale=request.guidance_scale or 5.0,
            negative_prompt=request.negative_prompt,
            api_key=request.api_key
        )
        
        return UnifiedImageResult(
            image_url=result.image_url,
            image_data=result.image_data,
            metadata={
                **result.metadata,
                'provider': 'hyperbolic',
                'width': request.width,
                'height': request.height
            },
            generation_time=result.generation_time,
            provider=ImageProvider.HYPERBOLIC,
            original_result=result
        )
    
    async def close(self):
        """Close all clients."""
        await self.modelscope_client.close()
        await self.hyperbolic_client.close()
        logger.info("Unified image client closed")
    
    def get_available_providers(self) -> list[str]:
        """Get list of available providers."""
        return [provider.value for provider in ImageProvider]
    
    def get_provider_info(self, provider: ImageProvider) -> dict:
        """Get information about a specific provider."""
        if provider == ImageProvider.MODELSCOPE:
            return {
                "name": "ModelScope",
                "description": "Alibaba's ModelScope platform with FLUX models",
                "models": ["MusePublic/489_ckpt_FLUX_1"],
                "supports": ["width", "height", "steps", "guidance_scale"],
                "max_resolution": "2048x2048"
            }
        elif provider == ImageProvider.HYPERBOLIC:
            return {
                "name": "Hyperbolic",
                "description": "Hyperbolic Labs image generation API",
                "models": ["FLUX.1-dev"],
                "supports": ["resolution", "steps", "cfg_scale", "negative_prompt", "strength"],
                "max_resolution": "1024x1024"
            }
        else:
            return {}
    
    async def test_provider(self, provider: ImageProvider, api_key: str) -> bool:
        """Test if a provider is working with the given API key."""
        try:
            test_request = UnifiedImageRequest(
                prompt="test image",
                provider=provider,
                width=512,
                height=512,
                api_key=api_key
            )
            
            # Try to generate a small test image
            await self.generate_image(test_request)
            return True
            
        except Exception as e:
            logger.warning(f"Provider {provider.value} test failed: {e}")
            return False
