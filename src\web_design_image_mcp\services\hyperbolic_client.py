"""
Hyperbolic API client for image generation.
"""

import asyncio
import json
import logging
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

import httpx
from PIL import Image
from io import BytesIO

from ..config import config

logger = logging.getLogger(__name__)


class HyperbolicAPIError(Exception):
    """Exception raised for Hyperbolic API errors."""
    pass


@dataclass
class HyperbolicImageRequest:
    """Request parameters for Hyperbolic image generation."""
    prompt: str
    model_name: str = "FLUX.1-dev"
    negative_prompt: str = ""
    resolution: str = "1024x1024"
    steps: str = "30"
    cfg_scale: str = "5"
    strength: str = "0.8"
    enable_refiner: str = "false"
    backend: str = "auto"


@dataclass
class HyperbolicImageResult:
    """Result from Hyperbolic image generation."""
    image_url: str
    image_data: bytes
    metadata: dict
    generation_time: datetime
    request_params: HyperbolicImageRequest


class HyperbolicClient:
    """Client for Hyperbolic image generation API."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize the Hyperbolic client.
        
        Args:
            api_key: Hyperbolic API key (optional, can be provided per request)
        """
        self.api_key = api_key or config.hyperbolic_api_key
        self.api_url = config.hyperbolic_api_url
        self.default_model = config.hyperbolic_model
        self.max_retries = config.max_retries
        self.retry_delay = config.retry_delay_seconds
        self.backoff_multiplier = config.backoff_multiplier
        
        # Create HTTP client
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(60.0),
            limits=httpx.Limits(max_connections=config.max_concurrent_requests)
        )
        
        logger.info("Hyperbolic client initialized")
    
    async def generate_image(
        self, 
        prompt: str, 
        resolution: str = "1024x1024",
        steps: int = 30,
        cfg_scale: float = 5.0,
        negative_prompt: str = "",
        strength: float = 0.8,
        enable_refiner: bool = False,
        api_key: Optional[str] = None
    ) -> HyperbolicImageResult:
        """Generate an image using the Hyperbolic API.
        
        Args:
            prompt: Text prompt for image generation
            resolution: Image resolution (e.g., "1024x1024")
            steps: Number of inference steps
            cfg_scale: CFG scale for generation
            negative_prompt: Negative prompt
            strength: Strength parameter
            enable_refiner: Whether to enable refiner
            api_key: API key to use (overrides instance key)
        
        Returns:
            HyperbolicImageResult with the generated image and metadata
        
        Raises:
            HyperbolicAPIError: If the API request fails
        """
        # Use provided API key or fall back to instance key
        auth_key = api_key or self.api_key
        if not auth_key:
            raise HyperbolicAPIError("No API key provided")
        
        # Prepare request
        request = HyperbolicImageRequest(
            prompt=prompt,
            model_name=self.default_model,
            negative_prompt=negative_prompt,
            resolution=resolution,
            steps=str(steps),
            cfg_scale=str(cfg_scale),
            strength=str(strength),
            enable_refiner=str(enable_refiner).lower(),
            backend="auto"
        )
        
        logger.info(f"Generating image with Hyperbolic: {prompt[:100]}...")
        
        # Make API request with retries
        for attempt in range(self.max_retries + 1):
            try:
                result = await self._make_api_request(request, auth_key)
                logger.info("Hyperbolic image generation successful")
                return result
            
            except Exception as e:
                if attempt == self.max_retries:
                    logger.error(f"Hyperbolic generation failed after {self.max_retries} retries: {e}")
                    raise HyperbolicAPIError(f"Failed to generate image: {e}")
                
                delay = self.retry_delay * (self.backoff_multiplier ** attempt)
                logger.warning(f"Attempt {attempt + 1} failed, retrying in {delay}s: {e}")
                await asyncio.sleep(delay)
    
    async def _make_api_request(
        self, 
        request: HyperbolicImageRequest, 
        api_key: str
    ) -> HyperbolicImageResult:
        """Make the actual API request to Hyperbolic."""
        payload = {
            "model_name": request.model_name,
            "prompt": request.prompt,
            "negative_prompt": request.negative_prompt,
            "resolution": request.resolution,
            "steps": request.steps,
            "cfg_scale": request.cfg_scale,
            "strength": request.strength,
            "enable_refiner": request.enable_refiner,
            "backend": request.backend
        }
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': f'Bearer {api_key}'
        }
        
        # Make the API request
        response = await self.client.post(
            self.api_url,
            json=payload,
            headers=headers
        )
        
        if response.status_code != 200:
            error_msg = f"API request failed with status {response.status_code}: {response.text}"
            raise HyperbolicAPIError(error_msg)
        
        # Parse response
        try:
            response_data = response.json()
            # Hyperbolic returns image URL in different format
            image_url = response_data.get('image_url') or response_data.get('images', [{}])[0].get('url')
            if not image_url:
                raise HyperbolicAPIError("No image URL in response")
        except (KeyError, IndexError, json.JSONDecodeError) as e:
            raise HyperbolicAPIError(f"Invalid API response format: {e}")
        
        # Download the image
        image_data = await self._download_image(image_url)
        
        # Create result
        result = HyperbolicImageResult(
            image_url=image_url,
            image_data=image_data,
            metadata={
                'model': request.model_name,
                'prompt': request.prompt,
                'resolution': request.resolution,
                'steps': request.steps,
                'cfg_scale': request.cfg_scale,
                'api_response': response_data
            },
            generation_time=datetime.now(),
            request_params=request
        )
        
        return result
    
    async def _download_image(self, image_url: str) -> bytes:
        """Download image from the provided URL."""
        try:
            response = await self.client.get(image_url)
            if response.status_code != 200:
                raise HyperbolicAPIError(f"Failed to download image: {response.status_code}")
            
            image_data = response.content
            
            # Validate image
            if not self._validate_image(image_data):
                raise HyperbolicAPIError("Downloaded image is invalid")
            
            return image_data
            
        except Exception as e:
            raise HyperbolicAPIError(f"Error downloading image: {e}")
    
    def _validate_image(self, image_data: bytes) -> bool:
        """Validate that the downloaded data is a valid image."""
        try:
            with Image.open(BytesIO(image_data)) as img:
                img.verify()
            return True
        except Exception:
            return False
    
    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
        logger.info("Hyperbolic client closed")
