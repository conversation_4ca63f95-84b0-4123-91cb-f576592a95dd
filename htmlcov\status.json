{"note": "This file is an internal implementation detail to speed up HTML report generation. Its format can change at any time. You might be looking for the JSON report: https://coverage.rtfd.io/cmd.html#cmd-json", "format": 5, "version": "7.9.2", "globals": "92b2bd1178ecb6d060ee2f452773a2e1", "files": {"z_b34453d2780a7b79___init___py": {"hash": "5bc51a8ef587ffd365adc0ca45b94b5f", "index": {"url": "z_b34453d2780a7b79___init___py.html", "file": "src\\web_design_image_mcp\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b34453d2780a7b79_config_py": {"hash": "83c94a3adc2e55ad5cfe1fea638cc653", "index": {"url": "z_b34453d2780a7b79_config_py.html", "file": "src\\web_design_image_mcp\\config.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 41, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_b34453d2780a7b79_server_py": {"hash": "0b1fe4e4fbe1e26d36ee82fb399e1632", "index": {"url": "z_b34453d2780a7b79_server_py.html", "file": "src\\web_design_image_mcp\\server.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 2, "n_missing": 36, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85a35d5be7025627___init___py": {"hash": "91d407e5f33c742995d7bb0563cbb598", "index": {"url": "z_85a35d5be7025627___init___py.html", "file": "src\\web_design_image_mcp\\services\\__init__.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 5, "n_excluded": 0, "n_missing": 0, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85a35d5be7025627_api_client_py": {"hash": "d5b2fc22c14eefc4dac56110e108df57", "index": {"url": "z_85a35d5be7025627_api_client_py.html", "file": "src\\web_design_image_mcp\\services\\api_client.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 87, "n_excluded": 0, "n_missing": 1, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85a35d5be7025627_context_analyzer_py": {"hash": "34542dd2989a45257c7a736203f4e7b0", "index": {"url": "z_85a35d5be7025627_context_analyzer_py.html", "file": "src\\web_design_image_mcp\\services\\context_analyzer.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 225, "n_excluded": 0, "n_missing": 41, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85a35d5be7025627_image_processor_py": {"hash": "7f4eca137d6fc67dc71afe3318006c92", "index": {"url": "z_85a35d5be7025627_image_processor_py.html", "file": "src\\web_design_image_mcp\\services\\image_processor.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 147, "n_excluded": 0, "n_missing": 62, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}, "z_85a35d5be7025627_prompt_generator_py": {"hash": "b46453d81be292c752418f722fdbcf74", "index": {"url": "z_85a35d5be7025627_prompt_generator_py.html", "file": "src\\web_design_image_mcp\\services\\prompt_generator.py", "description": "", "nums": {"precision": 0, "n_files": 1, "n_statements": 118, "n_excluded": 0, "n_missing": 3, "n_branches": 0, "n_partial_branches": 0, "n_missing_branches": 0}}}}}