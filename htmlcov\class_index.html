<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>Coverage report</title>
    <link rel="icon" sizes="32x32" href="favicon_32_cb_58284776.png">
    <link rel="stylesheet" href="style_cb_db813965.css" type="text/css">
    <script src="coverage_html_cb_497bf287.js" defer></script>
</head>
<body class="indexfile">
<header>
    <div class="content">
        <h1>Coverage report:
            <span class="pc_cov">81%</span>
        </h1>
        <aside id="help_panel_wrapper">
            <input id="help_panel_state" type="checkbox">
            <label for="help_panel_state">
                <img id="keyboard_icon" src="keybd_closed_cb_ce680311.png" alt="Show/hide keyboard shortcuts">
            </label>
            <div id="help_panel">
                <p class="legend">Shortcuts on this page</p>
                <div class="keyhelp">
                    <p>
                        <kbd>f</kbd>
                        <kbd>n</kbd>
                        <kbd>s</kbd>
                        <kbd>m</kbd>
                        <kbd>x</kbd>
                        <kbd>c</kbd>
                        &nbsp; change column sorting
                    </p>
                    <p>
                        <kbd>[</kbd>
                        <kbd>]</kbd>
                        &nbsp; prev/next file
                    </p>
                    <p>
                        <kbd>?</kbd> &nbsp; show/hide this help
                    </p>
                </div>
            </div>
        </aside>
        <form id="filter_container">
            <input id="filter" type="text" value="" placeholder="filter...">
            <div>
                <input id="hide100" type="checkbox" >
                <label for="hide100">hide covered</label>
            </div>
        </form>
        <h2>
                <a class="button" href="index.html">Files</a>
                <a class="button" href="function_index.html">Functions</a>
                <a class="button current">Classes</a>
        </h2>
        <p class="text">
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-13 20:31 -0700
        </p>
    </div>
</header>
<main id="index">
    <table class="index" data-sortable>
        <thead>
            <tr class="tablehead" title="Click to sort">
                <th id="file" class="name left" aria-sort="none" data-shortcut="f">File<span class="arrows"></span></th>
                <th id="region" class="name left" aria-sort="none" data-default-sort-order="ascending" data-shortcut="n">class<span class="arrows"></span></th>
                <th id="statements" aria-sort="none" data-default-sort-order="descending" data-shortcut="s">statements<span class="arrows"></span></th>
                <th id="missing" aria-sort="none" data-default-sort-order="descending" data-shortcut="m">missing<span class="arrows"></span></th>
                <th id="excluded" aria-sort="none" data-default-sort-order="descending" data-shortcut="x">excluded<span class="arrows"></span></th>
                <th id="coverage" class="right" aria-sort="none" data-shortcut="c">coverage<span class="arrows"></span></th>
            </tr>
        </thead>
        <tbody>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79___init___py.html">src\web_design_image_mcp\__init__.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html#t11">src\web_design_image_mcp\config.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html#t11"><data value='ServerConfig'>ServerConfig</data></a></td>
                <td>6</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="5 6">83%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html#t69">src\web_design_image_mcp\config.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html#t69"><data value='Config'>ServerConfig.Config</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html">src\web_design_image_mcp\config.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_config_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>35</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="35 35">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t32">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html#t32"><data value='ServerContext'>ServerContext</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html">src\web_design_image_mcp\server.py</a></td>
                <td class="name left"><a href="z_b34453d2780a7b79_server_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>118</td>
                <td>36</td>
                <td>2</td>
                <td class="right" data-ratio="82 118">69%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627___init___py.html">src\web_design_image_mcp\services\__init__.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627___init___py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>5</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="5 5">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t23">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t23"><data value='ImageGenerationRequest'>ImageGenerationRequest</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t32">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t32"><data value='ImageGenerationResult'>ImageGenerationResult</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t41">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t41"><data value='ModelScopeAPIError'>ModelScopeAPIError</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t46">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html#t46"><data value='ModelScopeClient'>ModelScopeClient</data></a></td>
                <td>54</td>
                <td>1</td>
                <td>0</td>
                <td class="right" data-ratio="53 54">98%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html">src\web_design_image_mcp\services\api_client.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_api_client_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>33</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="33 33">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t19">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t19"><data value='WebPageContext'>WebPageContext</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t31">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t31"><data value='ImagePlaceholder'>ImagePlaceholder</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t41">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html#t41"><data value='WebContextAnalyzer'>WebContextAnalyzer</data></a></td>
                <td>178</td>
                <td>41</td>
                <td>0</td>
                <td class="right" data-ratio="137 178">77%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html">src\web_design_image_mcp\services\context_analyzer.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_context_analyzer_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>47</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="47 47">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t25">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t25"><data value='ImageFormat'>ImageFormat</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t34">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t34"><data value='ProcessedImage'>ProcessedImage</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t43">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html#t43"><data value='ImageProcessor'>ImageProcessor</data></a></td>
                <td>109</td>
                <td>62</td>
                <td>0</td>
                <td class="right" data-ratio="47 109">43%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html">src\web_design_image_mcp\services\image_processor.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_image_processor_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>38</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="38 38">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t13">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t13"><data value='ImageType'>ImageType</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t27">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t27"><data value='WebsiteCategory'>WebsiteCategory</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t42">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t42"><data value='ImageContext'>ImageContext</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t55">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t55"><data value='PromptTemplate'>PromptTemplate</data></a></td>
                <td>0</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="0 0">100%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t63">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html#t63"><data value='PromptGenerator'>PromptGenerator</data></a></td>
                <td>64</td>
                <td>3</td>
                <td>0</td>
                <td class="right" data-ratio="61 64">95%</td>
            </tr>
            <tr class="region">
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html">src\web_design_image_mcp\services\prompt_generator.py</a></td>
                <td class="name left"><a href="z_85a35d5be7025627_prompt_generator_py.html"><data value=''><span class='no-noun'>(no class)</span></data></a></td>
                <td>54</td>
                <td>0</td>
                <td>0</td>
                <td class="right" data-ratio="54 54">100%</td>
            </tr>
        </tbody>
        <tfoot>
            <tr class="total">
                <td class="name left">Total</td>
                <td class="name left">&nbsp;</td>
                <td>746</td>
                <td>144</td>
                <td>2</td>
                <td class="right" data-ratio="602 746">81%</td>
            </tr>
        </tfoot>
    </table>
    <p id="no_rows">
        No items found using the specified filter.
    </p>
</main>
<footer>
    <div class="content">
        <p>
            <a class="nav" href="https://coverage.readthedocs.io/en/7.9.2">coverage.py v7.9.2</a>,
            created at 2025-07-13 20:31 -0700
        </p>
    </div>
    <aside class="hidden">
        <a id="prevFileLink" class="nav" href=""></a>
        <a id="nextFileLink" class="nav" href=""></a>
        <button type="button" class="button_prev_file" data-shortcut="["></button>
        <button type="button" class="button_next_file" data-shortcut="]"></button>
        <button type="button" class="button_show_hide_help" data-shortcut="?"></button>
    </aside>
</footer>
</body>
</html>
